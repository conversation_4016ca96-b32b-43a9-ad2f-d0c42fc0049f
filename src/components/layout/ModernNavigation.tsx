'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'

interface ModernNavigationProps {
  currentSection: string
}

const navigationItems = [
  { id: 'index', label: 'INDEX', labelCn: '首页' },
  { id: 'events', label: 'EVENTS', labelCn: '新闻' },
  { id: 'characters', label: 'CHARACTERS', labelCn: '角色' },
  { id: 'preset', label: 'PRESET', labelCn: '设定' },
  { id: 'world', label: 'WORLD', labelCn: '世界' },
  { id: 'more', label: 'MORE', labelCn: '更多内容' },
]

export default function ModernNavigation({ currentSection }: ModernNavigationProps) {
  const [showSocialPopup, setShowSocialPopup] = useState(false)
  const [showUserPopup, setShowUserPopup] = useState(false)
  const [isMuted, setIsMuted] = useState(false)

  return (
    <motion.nav
      className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-slate-900/95 via-slate-800/95 to-slate-900/95 backdrop-blur-md border-b border-cyan-500/20"
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      {/* 顶部装饰线 */}
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent"></div>

      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo区域 */}
          <motion.a
            href="/#index"
            className="flex items-center space-x-3 group"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-lg flex items-center justify-center shadow-lg shadow-cyan-500/25">
                <span className="text-white font-bold text-lg">明</span>
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-orange-400 rounded-full animate-pulse"></div>
            </div>
            <div className="hidden md:block">
              <div className="text-white font-bold text-xl tracking-wider group-hover:text-cyan-400 transition-colors">
                ARKNIGHTS
              </div>
              <div className="text-cyan-400/70 text-xs font-medium tracking-widest">
                心流元素
              </div>
            </div>
          </motion.a>

          {/* 主导航菜单 */}
          <div className="hidden lg:flex items-center space-x-8">
            {navigationItems.map((item, index) => (
              <motion.a
                key={item.id}
                href={`#${item.id}`}
                className="group relative px-4 py-2"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ y: -2 }}
              >
                <div className="text-center">
                  <div className={`font-bold text-sm tracking-wider transition-all duration-300 ${
                    currentSection === item.id 
                      ? 'text-cyan-400' 
                      : 'text-white/80 group-hover:text-cyan-300'
                  }`}>
                    {item.label}
                  </div>
                  <div className={`text-xs mt-1 transition-all duration-300 ${
                    currentSection === item.id 
                      ? 'text-cyan-300' 
                      : 'text-white/60 group-hover:text-cyan-200'
                  }`}>
                    {item.labelCn}
                  </div>
                </div>

                {/* 活跃状态指示器 */}
                {currentSection === item.id && (
                  <motion.div
                    className="absolute -bottom-1 left-1/2 transform -translate-x-1/2"
                    layoutId="activeIndicator"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  >
                    <div className="w-2 h-2 bg-cyan-400 rounded-full shadow-lg shadow-cyan-400/50"></div>
                    <div className="w-8 h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent mt-1"></div>
                  </motion.div>
                )}

                {/* 悬停背景效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
              </motion.a>
            ))}
          </div>

          {/* 右侧功能图标 */}
          <div className="flex items-center space-x-4">
            {/* 社交媒体 */}
            <motion.button
              onClick={() => setShowSocialPopup(!showSocialPopup)}
              className="w-10 h-10 bg-slate-800/50 hover:bg-cyan-500/20 rounded-lg flex items-center justify-center text-white/70 hover:text-cyan-400 transition-all duration-300 border border-slate-700/50 hover:border-cyan-500/30"
              whileHover={{ scale: 1.05, rotate: 5 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
              </svg>
            </motion.button>

            {/* 音频控制 */}
            <motion.button
              onClick={() => setIsMuted(!isMuted)}
              className="w-10 h-10 bg-slate-800/50 hover:bg-cyan-500/20 rounded-lg flex items-center justify-center text-white/70 hover:text-cyan-400 transition-all duration-300 border border-slate-700/50 hover:border-cyan-500/30"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {isMuted ? (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                </svg>
              )}
            </motion.button>

            {/* 用户中心 */}
            <motion.button
              onClick={() => setShowUserPopup(!showUserPopup)}
              className="w-10 h-10 bg-slate-800/50 hover:bg-cyan-500/20 rounded-lg flex items-center justify-center text-white/70 hover:text-cyan-400 transition-all duration-300 border border-slate-700/50 hover:border-cyan-500/30"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
              </svg>
            </motion.button>

            {/* 移动端菜单按钮 */}
            <motion.button
              className="lg:hidden w-10 h-10 bg-slate-800/50 hover:bg-cyan-500/20 rounded-lg flex flex-col justify-center items-center space-y-1 border border-slate-700/50 hover:border-cyan-500/30"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="w-5 h-0.5 bg-white/70 rounded-full"></span>
              <span className="w-5 h-0.5 bg-white/70 rounded-full"></span>
              <span className="w-5 h-0.5 bg-white/70 rounded-full"></span>
            </motion.button>
          </div>
        </div>
      </div>

      {/* 底部装饰线 */}
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-cyan-400/30 to-transparent"></div>

      {/* 社交媒体弹出层 */}
      {showSocialPopup && (
        <motion.div
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[100] flex items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={() => setShowSocialPopup(false)}
        >
          <motion.div
            className="bg-gradient-to-br from-slate-800 to-slate-900 border border-cyan-500/30 rounded-xl p-8 max-w-md shadow-2xl shadow-cyan-500/10"
            initial={{ scale: 0.8, opacity: 0, y: 50 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.8, opacity: 0, y: 50 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                </svg>
              </div>
              <h3 className="text-cyan-400 text-xl font-bold">社交媒体</h3>
            </div>
            <div className="space-y-4">
              {[
                { name: '官方微博', icon: '微', color: 'from-red-500 to-pink-500' },
                { name: '官方QQ群', icon: 'Q', color: 'from-blue-500 to-cyan-500' },
                { name: '官方微信', icon: '微', color: 'from-green-500 to-emerald-500' }
              ].map((social, index) => (
                <motion.a
                  key={social.name}
                  href="#"
                  className="flex items-center space-x-3 p-3 rounded-lg bg-slate-700/30 hover:bg-slate-700/50 transition-all duration-300 group"
                  whileHover={{ x: 5 }}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className={`w-8 h-8 bg-gradient-to-br ${social.color} rounded-lg flex items-center justify-center text-white font-bold text-sm`}>
                    {social.icon}
                  </div>
                  <span className="text-white/80 group-hover:text-cyan-300 transition-colors">
                    {social.name}
                  </span>
                </motion.a>
              ))}
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* 用户菜单弹出层 */}
      {showUserPopup && (
        <motion.div
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[100] flex items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={() => setShowUserPopup(false)}
        >
          <motion.div
            className="bg-gradient-to-br from-slate-800 to-slate-900 border border-cyan-500/30 rounded-xl p-8 max-w-md shadow-2xl shadow-cyan-500/10"
            initial={{ scale: 0.8, opacity: 0, y: 50 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.8, opacity: 0, y: 50 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
              </div>
              <h3 className="text-cyan-400 text-xl font-bold">用户中心</h3>
            </div>
            <div className="space-y-4">
              {[
                { name: '登录', icon: '登' },
                { name: '注册', icon: '注' },
                { name: '个人中心', icon: '个' }
              ].map((user, index) => (
                <motion.a
                  key={user.name}
                  href="#"
                  className="flex items-center space-x-3 p-3 rounded-lg bg-slate-700/30 hover:bg-slate-700/50 transition-all duration-300 group"
                  whileHover={{ x: 5 }}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="w-8 h-8 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                    {user.icon}
                  </div>
                  <span className="text-white/80 group-hover:text-cyan-300 transition-colors">
                    {user.name}
                  </span>
                </motion.a>
              ))}
            </div>
          </motion.div>
        </motion.div>
      )}
    </motion.nav>
  )
}
