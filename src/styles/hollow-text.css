/* 镂空文字效果样式 */

/* 方法1: mix-blend-mode 镂空效果 */
.hollow-text-blend {
  mix-blend-mode: difference;
  color: white;
}

/* 方法2: background-clip 渐变镂空 */
.hollow-text-gradient {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 方法3: 背景图片镂空 */
.hollow-text-image {
  background: url('/images/backgrounds/bg.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 方法4: 描边镂空效果 */
.hollow-text-stroke {
  color: transparent;
  -webkit-text-stroke: 2px white;
  text-stroke: 2px white;
}

/* 方法5: 阴影镂空效果 */
.hollow-text-shadow {
  color: transparent;
  text-shadow: 
    0 0 0 white,
    2px 2px 0 rgba(0,0,0,0.3),
    -2px -2px 0 rgba(0,0,0,0.3),
    2px -2px 0 rgba(0,0,0,0.3),
    -2px 2px 0 rgba(0,0,0,0.3);
}

/* 方法6: SVG mask 镂空 */
.hollow-text-mask {
  mask: url('#text-mask');
  -webkit-mask: url('#text-mask');
}