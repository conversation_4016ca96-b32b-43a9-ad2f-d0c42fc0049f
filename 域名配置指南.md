# 域名配置指南 - www.xinliuyuansu.com

## 🎯 目标
将域名 `www.xinliuyuansu.com` 解析到服务器 `60.205.169.140`

## 📋 DNS配置步骤

### 1. 登录域名管理后台
- 登录你的域名注册商（阿里云、腾讯云、GoDaddy等）
- 找到域名管理或DNS解析设置

### 2. 添加DNS记录

**记录1 - 根域名解析：**
```
记录类型: A
主机记录: @
记录值: 60.205.169.140
TTL: 600
```

**记录2 - www子域名解析：**
```
记录类型: A
主机记录: www  
记录值: 60.205.169.140
TTL: 600
```

### 3. 如果是阿里云域名的具体步骤：
1. 登录阿里云控制台
2. 进入"域名与网站" → "云解析DNS"
3. 点击域名 `xinliuyuansu.com` 进入解析设置
4. 点击"添加记录"，按上述配置添加两条A记录

## ✅ 服务器配置已完成

nginx配置已更新，支持以下访问方式：
- http://www.xinliuyuansu.com
- http://xinliuyuansu.com  
- http://60.205.169.140

## 🔍 验证步骤

### 1. 检查DNS解析（配置后10分钟-24小时生效）
```bash
# 检查根域名解析
nslookup xinliuyuansu.com

# 检查www子域名解析  
nslookup www.xinliuyuansu.com
```

### 2. 测试网站访问
```bash
# 测试HTTP响应
curl -I http://www.xinliuyuansu.com
curl -I http://xinliuyuansu.com
```

## 📝 注意事项

1. **DNS生效时间**：通常10分钟到24小时，耐心等待
2. **缓存清理**：可能需要清理本地DNS缓存
3. **HTTPS配置**：后续可以配置SSL证书启用HTTPS

## 🚀 后续优化建议

1. **SSL证书配置**：使用Let's Encrypt免费证书
2. **CDN加速**：可以考虑使用阿里云CDN
3. **域名重定向**：可以设置根域名自动跳转到www

配置完成后，你就可以通过 `www.xinliuyuansu.com` 访问你的心流元素网站了！
